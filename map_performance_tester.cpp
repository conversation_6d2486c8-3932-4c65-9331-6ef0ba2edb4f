#include "include/TowerDefense/GameLogic/Map.hpp"
#include "include/TowerDefense/Types.hpp"
#include <QCoreApplication>
#include <QDebug>
#include <chrono>
#include <iostream>
#include <vector>
#include <random>

class MapPerformanceTester {
public:
    struct PerformanceResult {
        std::string mapName;
        std::string operation;
        double averageTimeMs = 0.0;
        double minTimeMs = 0.0;
        double maxTimeMs = 0.0;
        int iterations = 0;
    };
    
    std::vector<PerformanceResult> testMapPerformance(const std::string& filePath) {
        std::vector<PerformanceResult> results;
        
        Map map;
        if (!map.loadFromFile(filePath)) {
            std::cout << "Failed to load map: " << filePath << std::endl;
            return results;
        }
        
        std::string mapName = extractMapName(filePath);
        
        // Test different operations
        results.push_back(testMapLoading(filePath, mapName));
        results.push_back(testTileAccess(map, mapName));
        results.push_back(testCoordinateConversion(map, mapName));
        results.push_back(testBuildabilityQueries(map, mapName));
        results.push_back(testPathAccess(map, mapName));
        
        return results;
    }
    
private:
    std::string extractMapName(const std::string& filePath) {
        size_t lastSlash = filePath.find_last_of('/');
        size_t lastDot = filePath.find_last_of('.');
        if (lastSlash != std::string::npos && lastDot != std::string::npos) {
            return filePath.substr(lastSlash + 1, lastDot - lastSlash - 1);
        }
        return filePath;
    }
    
    PerformanceResult testMapLoading(const std::string& filePath, const std::string& mapName) {
        PerformanceResult result;
        result.mapName = mapName;
        result.operation = "Map Loading";
        result.iterations = 100;
        
        std::vector<double> times;
        
        for (int i = 0; i < result.iterations; i++) {
            auto startTime = std::chrono::high_resolution_clock::now();
            
            Map map;
            map.loadFromFile(filePath);
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
            times.push_back(duration.count() / 1000.0);
        }
        
        calculateStats(times, result);
        return result;
    }
    
    PerformanceResult testTileAccess(const Map& map, const std::string& mapName) {
        PerformanceResult result;
        result.mapName = mapName;
        result.operation = "Tile Access";
        result.iterations = 10000;
        
        std::vector<double> times;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> xDist(0, map.getWidth() - 1);
        std::uniform_int_distribution<> yDist(0, map.getHeight() - 1);
        
        for (int i = 0; i < result.iterations; i++) {
            int x = xDist(gen);
            int y = yDist(gen);
            
            auto startTime = std::chrono::high_resolution_clock::now();
            
            TileType type = map.getTileType(x, y);
            (void)type; // Suppress unused variable warning
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            times.push_back(duration.count() / 1000000.0); // Convert to milliseconds
        }
        
        calculateStats(times, result);
        return result;
    }
    
    PerformanceResult testCoordinateConversion(const Map& map, const std::string& mapName) {
        PerformanceResult result;
        result.mapName = mapName;
        result.operation = "Coordinate Conversion";
        result.iterations = 10000;
        
        std::vector<double> times;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> xDist(0, map.getWidth() - 1);
        std::uniform_int_distribution<> yDist(0, map.getHeight() - 1);
        
        for (int i = 0; i < result.iterations; i++) {
            int x = xDist(gen);
            int y = yDist(gen);
            
            auto startTime = std::chrono::high_resolution_clock::now();
            
            QPointF worldPos = map.gridToWorld(x, y);
            QPoint gridPos = map.worldToGrid(worldPos);
            (void)gridPos; // Suppress unused variable warning
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            times.push_back(duration.count() / 1000000.0); // Convert to milliseconds
        }
        
        calculateStats(times, result);
        return result;
    }
    
    PerformanceResult testBuildabilityQueries(const Map& map, const std::string& mapName) {
        PerformanceResult result;
        result.mapName = mapName;
        result.operation = "Buildability Queries";
        result.iterations = 5000;
        
        std::vector<double> times;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> xDist(0, map.getWidth() - 1);
        std::uniform_int_distribution<> yDist(0, map.getHeight() - 1);
        std::uniform_int_distribution<> towerDist(0, 1);
        
        for (int i = 0; i < result.iterations; i++) {
            int x = xDist(gen);
            int y = yDist(gen);
            TowerType towerType = towerDist(gen) == 0 ? TowerType::MELEE : TowerType::RANGED;
            
            auto startTime = std::chrono::high_resolution_clock::now();
            
            bool buildable = map.isBuildable(x, y, towerType);
            (void)buildable; // Suppress unused variable warning
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            times.push_back(duration.count() / 1000000.0); // Convert to milliseconds
        }
        
        calculateStats(times, result);
        return result;
    }
    
    PerformanceResult testPathAccess(const Map& map, const std::string& mapName) {
        PerformanceResult result;
        result.mapName = mapName;
        result.operation = "Path Access";
        result.iterations = 1000;
        
        if (map.getPathCount() == 0) {
            result.averageTimeMs = 0.0;
            return result;
        }
        
        std::vector<double> times;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> pathDist(0, static_cast<int>(map.getPathCount()) - 1);
        
        for (int i = 0; i < result.iterations; i++) {
            int pathIndex = pathDist(gen);
            
            auto startTime = std::chrono::high_resolution_clock::now();
            
            auto const& path = map.getPath(pathIndex);
            if (!path.empty()) {
                auto waypoint = path[0]; // Access first waypoint
                (void)waypoint; // Suppress unused variable warning
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            times.push_back(duration.count() / 1000000.0); // Convert to milliseconds
        }
        
        calculateStats(times, result);
        return result;
    }
    
    void calculateStats(const std::vector<double>& times, PerformanceResult& result) {
        if (times.empty()) {
            return;
        }
        
        double sum = 0.0;
        result.minTimeMs = times[0];
        result.maxTimeMs = times[0];
        
        for (double time : times) {
            sum += time;
            if (time < result.minTimeMs) result.minTimeMs = time;
            if (time > result.maxTimeMs) result.maxTimeMs = time;
        }
        
        result.averageTimeMs = sum / times.size();
    }
};

void printPerformanceResults(const std::vector<MapPerformanceTester::PerformanceResult>& results) {
    if (results.empty()) return;
    
    std::cout << "\n" << std::string(80, '=') << std::endl;
    std::cout << "PERFORMANCE TEST RESULTS: " << results[0].mapName << std::endl;
    std::cout << std::string(80, '=') << std::endl;
    
    std::cout << std::left << std::setw(25) << "Operation"
              << std::setw(12) << "Avg (ms)"
              << std::setw(12) << "Min (ms)"
              << std::setw(12) << "Max (ms)"
              << std::setw(10) << "Iterations" << std::endl;
    std::cout << std::string(80, '-') << std::endl;
    
    for (const auto& result : results) {
        std::cout << std::left << std::setw(25) << result.operation
                  << std::setw(12) << std::fixed << std::setprecision(4) << result.averageTimeMs
                  << std::setw(12) << std::fixed << std::setprecision(4) << result.minTimeMs
                  << std::setw(12) << std::fixed << std::setprecision(4) << result.maxTimeMs
                  << std::setw(10) << result.iterations << std::endl;
    }
    std::cout << std::string(80, '=') << std::endl;
}

int main(int argc, char* argv[]) {
    QCoreApplication app(argc, argv);
    
    std::cout << "Tower Defense Map Performance Tester" << std::endl;
    std::cout << "====================================" << std::endl;
    
    MapPerformanceTester tester;
    
    std::vector<std::string> testMaps = {
        "assets/data/maps/StandardMap.map",
        "assets/data/maps/ComplexSnakeMap.map",
        "assets/data/maps/test_maps/LargeMaze.map",
        "assets/data/maps/test_maps/MediumSpiral.map",
        "assets/data/maps/test_maps/MultiplePaths.map"
    };
    
    for (const auto& mapPath : testMaps) {
        auto results = tester.testMapPerformance(mapPath);
        printPerformanceResults(results);
    }
    
    return 0;
}
