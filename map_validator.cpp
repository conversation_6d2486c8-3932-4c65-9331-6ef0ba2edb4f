#include "include/TowerDefense/GameLogic/Map.hpp"
#include "include/TowerDefense/Types.hpp"
#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <iostream>
#include <set>
#include <vector>

class MapValidator {
public:
    struct ValidationResult {
        bool isValid = false;
        std::string mapName;
        std::string filePath;
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
        
        // Statistics
        int width = 0, height = 0;
        float tileSize = 0.0f;
        int pathCount = 0;
        int startPoints = 0, endPoints = 0, pathTiles = 0;
        int buildableGround = 0, obstacles = 0;
        int meleeBuildable = 0, rangedBuildable = 0;
    };
    
    ValidationResult validateMap(const std::string& filePath) {
        ValidationResult result;
        result.filePath = filePath;
        
        Map testMap;
        bool loadSuccess = testMap.loadFromFile(filePath);
        
        if (!loadSuccess) {
            result.errors.push_back("Failed to load map file");
            return result;
        }
        
        result.isValid = true;
        result.width = testMap.getWidth();
        result.height = testMap.getHeight();
        result.tileSize = testMap.getTileSize();
        result.pathCount = static_cast<int>(testMap.getPathCount());
        
        // Extract map name from file path
        QFileInfo fileInfo(QString::fromStdString(filePath));
        result.mapName = fileInfo.baseName().toStdString();
        
        // Count tile types and validate
        validateTileTypes(testMap, result);
        validatePaths(testMap, result);
        validateBuildability(testMap, result);
        validateGameplayBalance(testMap, result);
        
        return result;
    }
    
private:
    void validateTileTypes(const Map& map, ValidationResult& result) {
        for (int y = 0; y < map.getHeight(); y++) {
            for (int x = 0; x < map.getWidth(); x++) {
                TileType type = map.getTileType(x, y);
                switch (type) {
                    case TileType::START_POINT: result.startPoints++; break;
                    case TileType::END_POINT: result.endPoints++; break;
                    case TileType::PATH: result.pathTiles++; break;
                    case TileType::BUILDABLE_GROUND: result.buildableGround++; break;
                    case TileType::OBSTACLE: result.obstacles++; break;
                    case TileType::INVALID:
                        result.errors.push_back("Invalid tile type found at (" + 
                                              std::to_string(x) + "," + std::to_string(y) + ")");
                        result.isValid = false;
                        break;
                }
            }
        }
        
        // Validate tile type counts
        if (result.startPoints == 0) {
            result.errors.push_back("No start points found");
            result.isValid = false;
        }
        if (result.endPoints == 0) {
            result.errors.push_back("No end points found");
            result.isValid = false;
        }
        if (result.pathTiles == 0 && result.startPoints <= 1 && result.endPoints <= 1) {
            result.warnings.push_back("Very few path tiles - may be too simple");
        }
    }
    
    void validatePaths(const Map& map, ValidationResult& result) {
        if (map.getPathCount() == 0) {
            result.errors.push_back("No paths defined");
            result.isValid = false;
            return;
        }
        
        for (size_t i = 0; i < map.getPathCount(); i++) {
            auto const& path = map.getPath(i);
            if (path.empty()) {
                result.errors.push_back("Path " + std::to_string(i) + " is empty");
                result.isValid = false;
                continue;
            }
            
            // Validate path start and end
            auto start = path.front();
            auto end = path.back();
            
            TileType startType = map.getTileType(static_cast<int>(start.x()), static_cast<int>(start.y()));
            TileType endType = map.getTileType(static_cast<int>(end.x()), static_cast<int>(end.y()));
            
            if (startType != TileType::START_POINT) {
                result.errors.push_back("Path " + std::to_string(i) + " does not start at START_POINT");
                result.isValid = false;
            }
            if (endType != TileType::END_POINT) {
                result.errors.push_back("Path " + std::to_string(i) + " does not end at END_POINT");
                result.isValid = false;
            }
            
            // Validate path continuity
            for (size_t j = 1; j < path.size(); j++) {
                auto prev = path[j-1];
                auto curr = path[j];
                float distance = std::sqrt(std::pow(curr.x() - prev.x(), 2) + std::pow(curr.y() - prev.y(), 2));
                if (distance > 1.5f) { // Allow for diagonal movement
                    result.warnings.push_back("Path " + std::to_string(i) + " has large gap between waypoints " + 
                                            std::to_string(j-1) + " and " + std::to_string(j));
                }
            }
        }
    }
    
    void validateBuildability(const Map& map, ValidationResult& result) {
        for (int y = 0; y < map.getHeight(); y++) {
            for (int x = 0; x < map.getWidth(); x++) {
                if (map.isBuildable(x, y, TowerType::MELEE)) {
                    result.meleeBuildable++;
                }
                if (map.isBuildable(x, y, TowerType::RANGED)) {
                    result.rangedBuildable++;
                }
            }
        }
        
        if (result.meleeBuildable == 0) {
            result.warnings.push_back("No positions available for melee towers");
        }
        if (result.rangedBuildable == 0) {
            result.warnings.push_back("No positions available for ranged towers");
        }
    }
    
    void validateGameplayBalance(const Map& map, ValidationResult& result) {
        int totalTiles = map.getWidth() * map.getHeight();
        float pathRatio = static_cast<float>(result.pathTiles + result.startPoints + result.endPoints) / totalTiles;
        float buildableRatio = static_cast<float>(result.buildableGround) / totalTiles;
        
        if (pathRatio < 0.1f) {
            result.warnings.push_back("Very low path coverage (" + std::to_string(pathRatio * 100) + "%) - may be too easy");
        }
        if (pathRatio > 0.7f) {
            result.warnings.push_back("Very high path coverage (" + std::to_string(pathRatio * 100) + "%) - may be too hard");
        }
        if (buildableRatio < 0.1f) {
            result.warnings.push_back("Very low buildable area (" + std::to_string(buildableRatio * 100) + "%) - limited strategy");
        }
    }
};

void printValidationResult(const MapValidator::ValidationResult& result) {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "Map: " << result.mapName << std::endl;
    std::cout << "File: " << result.filePath << std::endl;
    std::cout << "Status: " << (result.isValid ? "✓ VALID" : "✗ INVALID") << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    
    // Basic properties
    std::cout << "Dimensions: " << result.width << "x" << result.height << std::endl;
    std::cout << "Tile Size: " << result.tileSize << std::endl;
    std::cout << "Paths: " << result.pathCount << std::endl;
    
    // Tile counts
    std::cout << "\nTile Distribution:" << std::endl;
    std::cout << "  Start Points: " << result.startPoints << std::endl;
    std::cout << "  End Points: " << result.endPoints << std::endl;
    std::cout << "  Path Tiles: " << result.pathTiles << std::endl;
    std::cout << "  Buildable Ground: " << result.buildableGround << std::endl;
    std::cout << "  Obstacles: " << result.obstacles << std::endl;
    
    // Buildability
    std::cout << "\nBuildability:" << std::endl;
    std::cout << "  Melee Tower Positions: " << result.meleeBuildable << std::endl;
    std::cout << "  Ranged Tower Positions: " << result.rangedBuildable << std::endl;
    
    // Errors
    if (!result.errors.empty()) {
        std::cout << "\nErrors:" << std::endl;
        for (const auto& error : result.errors) {
            std::cout << "  ✗ " << error << std::endl;
        }
    }
    
    // Warnings
    if (!result.warnings.empty()) {
        std::cout << "\nWarnings:" << std::endl;
        for (const auto& warning : result.warnings) {
            std::cout << "  ⚠ " << warning << std::endl;
        }
    }
}

int main(int argc, char* argv[]) {
    QCoreApplication app(argc, argv);
    
    std::cout << "Tower Defense Map Validator" << std::endl;
    std::cout << "===========================" << std::endl;
    
    MapValidator validator;
    std::vector<std::string> testMaps = {
        "assets/data/maps/StandardMap.map",
        "assets/data/maps/ComplexSnakeMap.map",
        "assets/data/maps/test_maps/SmallLinear.map",
        "assets/data/maps/test_maps/SmallLShape.map",
        "assets/data/maps/test_maps/MediumSpiral.map",
        "assets/data/maps/test_maps/MultiplePaths.map",
        "assets/data/maps/test_maps/LargeMaze.map",
        "assets/data/maps/test_maps/EdgeCase_SingleTile.map",
        "assets/data/maps/test_maps/EdgeCase_MaxBuildable.map",
        "assets/data/maps/test_maps/EdgeCase_MinBuildable.map",
        "assets/data/maps/test_maps/EdgeCase_AllObstacles.map",
        "assets/data/maps/test_maps/Error_MismatchedDimensions.map",
        "assets/data/maps/test_maps/Error_MissingPath.map",
        "assets/data/maps/test_maps/Error_InvalidCharacters.map"
    };
    
    int validMaps = 0;
    int totalMaps = 0;
    
    for (const auto& mapPath : testMaps) {
        auto result = validator.validateMap(mapPath);
        printValidationResult(result);
        
        if (result.isValid) {
            validMaps++;
        }
        totalMaps++;
    }
    
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "Validation Summary: " << validMaps << "/" << totalMaps << " maps valid" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    
    return 0;
}
