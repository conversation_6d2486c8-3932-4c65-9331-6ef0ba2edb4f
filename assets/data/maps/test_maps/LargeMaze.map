# Tower Defense Map File - Large Maze Test Map
# 25x20 map with complex maze-like path for stress testing

[Metadata]
name = Large Maze Test
width = 25
height = 20
tileSize = 64.0
initialLives = 30
initialGold = 800

[Tiles]
# Complex maze layout
WWWWWWWWWWWWWWWWWWWWWWWWW
WGGGGGGGGGGGGGGGGGGGGGGGW
WSPPPPPPPPPPPPPPPPPPPPPGW
WGWWWWWWWWWWWWWWWWWWWWPGW
WGPPPPPPPPPPPPPPPPPPPGPGW
WGPGWWWWWWWWWWWWWWWGPGPGW
WGPGPPPPPPPPPPPPPPPGPGPGW
WGPGPGWWWWWWWWWWWGPGPGPGW
WGPGPGPPPPPPPPPPPGPGPGPGW
WGPGPGPGWWWWWWWGPGPGPGPGW
WGPGPGPGPPPPPPPGPGPGPGPGW
WGPGPGPGPGWWWGPGPGPGPGPGW
WGPGPGPGPGPPPGPGPGPGPGPGW
WGPGPGPGPGPGWGPGPGPGPGPGW
WGPGPGPGPGPPPPPGPGPGPGPGW
WGPGPGPGPGWWWWWGPGPGPGPGW
WGPGPGPGPPPPPPPPPGPGPGPGW
WGPGPGPWWWWWWWWWWGPGPGPGW
WGPGPGPPPPPPPPPPPPPGPGPEW
WWWWWWWWWWWWWWWWWWWWWWWWW

[Paths]
# Complex maze path - winding through the entire map
Path:0
1,2   # S - start point
2,2   # P - right
3,2   # P
4,2   # P
5,2   # P
6,2   # P
7,2   # P
8,2   # P
9,2   # P
10,2  # P
11,2  # P
12,2  # P
13,2  # P
14,2  # P
15,2  # P
16,2  # P
17,2  # P
18,2  # P
19,2  # P
20,2  # P
21,2  # P
22,2  # P
22,3  # P - down
22,4  # P - down
20,4  # P - left
19,4  # P
18,4  # P
17,4  # P
16,4  # P
15,4  # P
14,4  # P
13,4  # P
12,4  # P
11,4  # P
10,4  # P
9,4   # P
8,4   # P
7,4   # P
6,4   # P
5,4   # P
4,4   # P
3,4   # P
2,4   # P
2,5   # P - down
2,6   # P - down
3,6   # P - right
4,6   # P
5,6   # P
6,6   # P
7,6   # P
8,6   # P
9,6   # P
10,6  # P
11,6  # P
12,6  # P
13,6  # P
14,6  # P
15,6  # P
16,6  # P
17,6  # P
18,6  # P
20,6  # P
22,6  # P
22,7  # P - down
20,7  # P - left
18,7  # P
16,7  # P
14,7  # P
12,7  # P
10,7  # P
8,7   # P
6,7   # P
4,7   # P
2,7   # P
2,8   # P - down
4,8   # P - right
6,8   # P
8,8   # P
10,8  # P
12,8  # P
14,8  # P
16,8  # P
18,8  # P
20,8  # P
22,8  # P
22,9  # P - down
20,9  # P - left
18,9  # P
16,9  # P
14,9  # P
12,9  # P
10,9  # P
8,9   # P
6,9   # P
4,9   # P
2,9   # P
2,10  # P - down
4,10  # P - right
6,10  # P
8,10  # P
10,10 # P
12,10 # P
14,10 # P
16,10 # P
18,10 # P
20,10 # P
22,10 # P
22,11 # P - down
20,11 # P - left
18,11 # P
16,11 # P
14,11 # P
12,11 # P
10,11 # P
8,11  # P
6,11  # P
4,11  # P
2,11  # P
2,12  # P - down
4,12  # P - right
6,12  # P
8,12  # P
10,12 # P
12,12 # P
14,12 # P
16,12 # P
18,12 # P
20,12 # P
22,12 # P
22,13 # P - down
20,13 # P - left
18,13 # P
16,13 # P
14,13 # P
12,13 # P
10,13 # P
8,13  # P
6,13  # P
4,13  # P
2,13  # P
2,14  # P - down
4,14  # P - right
6,14  # P
8,14  # P
10,14 # P
12,14 # P
14,14 # P
16,14 # P
18,14 # P
20,14 # P
22,14 # P
22,15 # P - down
20,15 # P - left
18,15 # P
16,15 # P
14,15 # P
12,15 # P
10,15 # P
8,15  # P
6,15  # P
4,15  # P
2,15  # P
2,16  # P - down
4,16  # P - right
6,16  # P
8,16  # P
10,16 # P
12,16 # P
14,16 # P
16,16 # P
18,16 # P
20,16 # P
22,16 # P
22,17 # P - down
20,17 # P - left
18,17 # P
16,17 # P
14,17 # P
12,17 # P
10,17 # P
8,17  # P
6,17  # P
4,17  # P
2,17  # P
2,18  # P - down
3,18  # P - right
4,18  # P
5,18  # P
6,18  # P
7,18  # P
8,18  # P
9,18  # P
10,18 # P
11,18 # P
12,18 # P
13,18 # P
14,18 # P
15,18 # P
16,18 # P
17,18 # P
18,18 # P
19,18 # P
20,18 # P
21,18 # P
22,18 # P
23,18 # E - end point
