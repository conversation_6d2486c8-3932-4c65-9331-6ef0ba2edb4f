#include "include/TowerDefense/GameLogic/Map.hpp"
#include "include/TowerDefense/Types.hpp"
#include <QCoreApplication>
#include <QDebug>
#include <iostream>
#include <iomanip>
#include <sstream>

class MapVisualizer {
public:
    void visualizeMap(const std::string& filePath) {
        Map map;
        if (!map.loadFromFile(filePath)) {
            std::cout << "Failed to load map: " << filePath << std::endl;
            return;
        }
        
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "Map Visualization: " << filePath << std::endl;
        std::cout << std::string(60, '=') << std::endl;
        
        printMapInfo(map);
        printTileGrid(map);
        printPathInfo(map);
        printBuildabilityGrid(map);
    }
    
private:
    void printMapInfo(const Map& map) {
        std::cout << "Dimensions: " << map.getWidth() << "x" << map.getHeight() << std::endl;
        std::cout << "Tile Size: " << map.getTileSize() << std::endl;
        std::cout << "Paths: " << map.getPathCount() << std::endl;
        std::cout << std::endl;
    }
    
    void printTileGrid(const Map& map) {
        std::cout << "Tile Layout:" << std::endl;
        std::cout << "Legend: G=Buildable, P=Path, S=Start, E=End, W=Wall, ?=Invalid" << std::endl;
        
        // Print column numbers
        std::cout << "   ";
        for (int x = 0; x < map.getWidth(); x++) {
            std::cout << std::setw(2) << x % 10;
        }
        std::cout << std::endl;
        
        // Print grid with row numbers
        for (int y = 0; y < map.getHeight(); y++) {
            std::cout << std::setw(2) << y << " ";
            for (int x = 0; x < map.getWidth(); x++) {
                char tileChar = getTileChar(map.getTileType(x, y));
                std::cout << tileChar << " ";
            }
            std::cout << std::endl;
        }
        std::cout << std::endl;
    }
    
    void printPathInfo(const Map& map) {
        std::cout << "Path Details:" << std::endl;
        for (size_t i = 0; i < map.getPathCount(); i++) {
            auto const& path = map.getPath(i);
            std::cout << "Path " << i << ": " << path.size() << " waypoints" << std::endl;
            
            if (!path.empty()) {
                auto start = path.front();
                auto end = path.back();
                std::cout << "  Start: (" << start.x() << ", " << start.y() << ")" << std::endl;
                std::cout << "  End: (" << end.x() << ", " << end.y() << ")" << std::endl;
                
                // Print first few and last few waypoints
                std::cout << "  Waypoints: ";
                size_t printCount = std::min(static_cast<size_t>(5), path.size());
                for (size_t j = 0; j < printCount; j++) {
                    std::cout << "(" << path[j].x() << "," << path[j].y() << ")";
                    if (j < printCount - 1) std::cout << " -> ";
                }
                if (path.size() > 5) {
                    std::cout << " ... (" << path.back().x() << "," << path.back().y() << ")";
                }
                std::cout << std::endl;
            }
        }
        std::cout << std::endl;
    }
    
    void printBuildabilityGrid(const Map& map) {
        std::cout << "Buildability Analysis:" << std::endl;
        std::cout << "Legend: M=Melee only, R=Ranged only, B=Both, .=None" << std::endl;
        
        // Print column numbers
        std::cout << "   ";
        for (int x = 0; x < map.getWidth(); x++) {
            std::cout << std::setw(2) << x % 10;
        }
        std::cout << std::endl;
        
        // Print buildability grid
        for (int y = 0; y < map.getHeight(); y++) {
            std::cout << std::setw(2) << y << " ";
            for (int x = 0; x < map.getWidth(); x++) {
                bool canMelee = map.isBuildable(x, y, TowerType::MELEE);
                bool canRanged = map.isBuildable(x, y, TowerType::RANGED);
                
                char buildChar = '.';
                if (canMelee && canRanged) {
                    buildChar = 'B';
                } else if (canMelee) {
                    buildChar = 'M';
                } else if (canRanged) {
                    buildChar = 'R';
                }
                
                std::cout << buildChar << " ";
            }
            std::cout << std::endl;
        }
        std::cout << std::endl;
    }
    
    char getTileChar(TileType type) {
        switch (type) {
            case TileType::BUILDABLE_GROUND: return 'G';
            case TileType::PATH: return 'P';
            case TileType::START_POINT: return 'S';
            case TileType::END_POINT: return 'E';
            case TileType::OBSTACLE: return 'W';
            case TileType::INVALID: return '?';
            default: return '?';
        }
    }
};

int main(int argc, char* argv[]) {
    QCoreApplication app(argc, argv);
    
    std::cout << "Tower Defense Map Visualizer" << std::endl;
    std::cout << "============================" << std::endl;
    
    MapVisualizer visualizer;
    
    std::vector<std::string> testMaps = {
        "assets/data/maps/StandardMap.map",
        "assets/data/maps/ComplexSnakeMap.map",
        "assets/data/maps/test_maps/SmallLinear.map",
        "assets/data/maps/test_maps/SmallLShape.map",
        "assets/data/maps/test_maps/MediumSpiral.map",
        "assets/data/maps/test_maps/MultiplePaths.map",
        "assets/data/maps/test_maps/EdgeCase_SingleTile.map",
        "assets/data/maps/test_maps/EdgeCase_MaxBuildable.map",
        "assets/data/maps/test_maps/EdgeCase_MinBuildable.map",
        "assets/data/maps/test_maps/EdgeCase_AllObstacles.map"
    };
    
    // If command line argument provided, visualize specific map
    if (argc > 1) {
        std::string mapPath = argv[1];
        visualizer.visualizeMap(mapPath);
    } else {
        // Visualize all test maps
        for (const auto& mapPath : testMaps) {
            visualizer.visualizeMap(mapPath);
        }
    }
    
    return 0;
}
