#include "include/TowerDefense/GameLogic/Map.hpp"
#include "include/TowerDefense/Types.hpp"
#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <chrono>
#include <iostream>
#include <vector>

class AutomatedMapTester {
public:
    struct TestResult {
        std::string mapName;
        std::string filePath;
        bool loadSuccess = false;
        bool validationSuccess = false;
        double loadTimeMs = 0.0;
        std::string errorMessage;
        
        // Map properties
        int width = 0, height = 0;
        int pathCount = 0;
        int totalTiles = 0;
    };
    
    std::vector<TestResult> runAllTests() {
        std::vector<TestResult> results;
        
        std::vector<std::string> testMaps = {
            "assets/data/maps/StandardMap.map",
            "assets/data/maps/ComplexSnakeMap.map",
            "assets/data/maps/test_maps/SmallLinear.map",
            "assets/data/maps/test_maps/SmallLShape.map",
            "assets/data/maps/test_maps/MediumSpiral.map",
            "assets/data/maps/test_maps/MultiplePaths.map",
            "assets/data/maps/test_maps/LargeMaze.map",
            "assets/data/maps/test_maps/EdgeCase_SingleTile.map",
            "assets/data/maps/test_maps/EdgeCase_MaxBuildable.map",
            "assets/data/maps/test_maps/EdgeCase_MinBuildable.map",
            "assets/data/maps/test_maps/EdgeCase_AllObstacles.map",
            "assets/data/maps/test_maps/Error_MismatchedDimensions.map",
            "assets/data/maps/test_maps/Error_MissingPath.map",
            "assets/data/maps/test_maps/Error_InvalidCharacters.map"
        };
        
        for (const auto& mapPath : testMaps) {
            results.push_back(testMap(mapPath));
        }
        
        return results;
    }
    
    TestResult testMap(const std::string& filePath) {
        TestResult result;
        result.filePath = filePath;
        
        // Extract map name
        QFileInfo fileInfo(QString::fromStdString(filePath));
        result.mapName = fileInfo.baseName().toStdString();
        
        // Test map loading with timing
        auto startTime = std::chrono::high_resolution_clock::now();
        
        Map testMap;
        result.loadSuccess = testMap.loadFromFile(filePath);
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
        result.loadTimeMs = duration.count() / 1000.0;
        
        if (!result.loadSuccess) {
            result.errorMessage = "Failed to load map file";
            return result;
        }
        
        // Extract basic properties
        result.width = testMap.getWidth();
        result.height = testMap.getHeight();
        result.pathCount = static_cast<int>(testMap.getPathCount());
        result.totalTiles = result.width * result.height;
        
        // Run additional validation tests
        result.validationSuccess = runValidationTests(testMap, result);
        
        return result;
    }
    
private:
    bool runValidationTests(const Map& map, TestResult& result) {
        try {
            // Test coordinate conversion
            for (int i = 0; i < 10; i++) {
                int x = i % map.getWidth();
                int y = i % map.getHeight();
                QPointF worldPos = map.gridToWorld(x, y);
                QPoint gridPos = map.worldToGrid(worldPos);
                
                if (gridPos.x() != x || gridPos.y() != y) {
                    result.errorMessage = "Coordinate conversion failed for (" + 
                                        std::to_string(x) + "," + std::to_string(y) + ")";
                    return false;
                }
            }
            
            // Test tile type access
            for (int y = 0; y < map.getHeight(); y++) {
                for (int x = 0; x < map.getWidth(); x++) {
                    TileType type = map.getTileType(x, y);
                    if (type == TileType::INVALID) {
                        result.errorMessage = "Invalid tile type at (" + 
                                            std::to_string(x) + "," + std::to_string(y) + ")";
                        return false;
                    }
                }
            }
            
            // Test buildability queries
            int buildableCount = 0;
            for (int y = 0; y < map.getHeight(); y++) {
                for (int x = 0; x < map.getWidth(); x++) {
                    if (map.isBuildableAt(QPoint(x, y))) {
                        buildableCount++;
                    }
                }
            }
            
            // Test path access
            for (size_t i = 0; i < map.getPathCount(); i++) {
                auto const& path = map.getPath(i);
                if (path.empty()) {
                    result.errorMessage = "Empty path found at index " + std::to_string(i);
                    return false;
                }
            }
            
            return true;
            
        } catch (const std::exception& e) {
            result.errorMessage = "Exception during validation: " + std::string(e.what());
            return false;
        } catch (...) {
            result.errorMessage = "Unknown exception during validation";
            return false;
        }
    }
};

void printTestSummary(const std::vector<AutomatedMapTester::TestResult>& results) {
    std::cout << "\n" << std::string(80, '=') << std::endl;
    std::cout << "AUTOMATED MAP TEST SUMMARY" << std::endl;
    std::cout << std::string(80, '=') << std::endl;
    
    int totalMaps = results.size();
    int loadSuccesses = 0;
    int validationSuccesses = 0;
    double totalLoadTime = 0.0;
    double maxLoadTime = 0.0;
    std::string slowestMap;
    
    // Print individual results
    std::cout << std::left << std::setw(25) << "Map Name" 
              << std::setw(8) << "Load" 
              << std::setw(8) << "Valid" 
              << std::setw(10) << "Time(ms)"
              << std::setw(8) << "Size"
              << std::setw(8) << "Paths"
              << "Error" << std::endl;
    std::cout << std::string(80, '-') << std::endl;
    
    for (const auto& result : results) {
        loadSuccesses += result.loadSuccess ? 1 : 0;
        validationSuccesses += result.validationSuccess ? 1 : 0;
        totalLoadTime += result.loadTimeMs;
        
        if (result.loadTimeMs > maxLoadTime) {
            maxLoadTime = result.loadTimeMs;
            slowestMap = result.mapName;
        }
        
        std::cout << std::left << std::setw(25) << result.mapName
                  << std::setw(8) << (result.loadSuccess ? "✓" : "✗")
                  << std::setw(8) << (result.validationSuccess ? "✓" : "✗")
                  << std::setw(10) << std::fixed << std::setprecision(2) << result.loadTimeMs
                  << std::setw(8) << (std::to_string(result.width) + "x" + std::to_string(result.height))
                  << std::setw(8) << result.pathCount;
        
        if (!result.errorMessage.empty()) {
            std::cout << result.errorMessage;
        }
        std::cout << std::endl;
    }
    
    // Print summary statistics
    std::cout << std::string(80, '-') << std::endl;
    std::cout << "Total Maps Tested: " << totalMaps << std::endl;
    std::cout << "Load Successes: " << loadSuccesses << "/" << totalMaps 
              << " (" << (100.0 * loadSuccesses / totalMaps) << "%)" << std::endl;
    std::cout << "Validation Successes: " << validationSuccesses << "/" << totalMaps 
              << " (" << (100.0 * validationSuccesses / totalMaps) << "%)" << std::endl;
    std::cout << "Average Load Time: " << std::fixed << std::setprecision(2) 
              << (totalLoadTime / totalMaps) << " ms" << std::endl;
    std::cout << "Slowest Map: " << slowestMap << " (" << maxLoadTime << " ms)" << std::endl;
    
    // Overall result
    bool allPassed = (loadSuccesses == totalMaps) && (validationSuccesses == totalMaps);
    std::cout << "\nOverall Result: " << (allPassed ? "✓ ALL TESTS PASSED" : "✗ SOME TESTS FAILED") << std::endl;
    std::cout << std::string(80, '=') << std::endl;
}

int main(int argc, char* argv[]) {
    QCoreApplication app(argc, argv);
    
    std::cout << "Tower Defense Automated Map Tester" << std::endl;
    std::cout << "==================================" << std::endl;
    
    AutomatedMapTester tester;
    auto results = tester.runAllTests();
    
    printTestSummary(results);
    
    // Return appropriate exit code
    bool allPassed = std::all_of(results.begin(), results.end(), 
                                [](const AutomatedMapTester::TestResult& r) {
                                    return r.loadSuccess && r.validationSuccess;
                                });
    
    return allPassed ? 0 : 1;
}
