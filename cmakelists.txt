cmake_minimum_required(VERSION 3.10)

project(TowerDefense VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    add_compile_options(-Wall -Wextra -Wpedantic)
    add_compile_options(-Werror)
endif()

# --- 查找和链接库 ---
find_package(Qt6 COMPONENTS Core Gui Widgets REQUIRED)

if(POLICY CMP0167)
    cmake_policy(SET CMP0167 OLD)
endif()

find_package(Boost REQUIRED)
set(BOOST_ROOT "/opt/homebrew/Cellar/boost/1.88.0")
include_directories(${Boost_INCLUDE_DIRS})

# --- 添加子目录 ---
add_subdirectory(src/Core)
add_subdirectory(src/GameLogic)
add_subdirectory(src/Utils)
add_subdirectory(src/Gui)
add_subdirectory(apps/TowerDefenseGame)

# --- 测试工具 ---
# Map Validator
add_executable(map_validator map_validator.cpp)
target_link_libraries(map_validator
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(map_validator PRIVATE include)

# Map Visualizer
add_executable(map_visualizer map_visualizer.cpp)
target_link_libraries(map_visualizer
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(map_visualizer PRIVATE include)

# Automated Map Tester
add_executable(automated_map_tester automated_map_tester.cpp)
target_link_libraries(automated_map_tester
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(automated_map_tester PRIVATE include)

# Map Performance Tester
add_executable(map_performance_tester map_performance_tester.cpp)
target_link_libraries(map_performance_tester
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(map_performance_tester PRIVATE include)

# Existing test programs
add_executable(test_map_loading test_map_loading.cpp)
target_link_libraries(test_map_loading
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(test_map_loading PRIVATE include)

add_executable(comprehensive_map_test comprehensive_map_test.cpp)
target_link_libraries(comprehensive_map_test
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(comprehensive_map_test PRIVATE include)