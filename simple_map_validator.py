#!/usr/bin/env python3
"""
Simple Map Validator for Tower Defense Maps
Validates map format and structure without requiring C++ compilation
"""

import os
import sys
from pathlib import Path
import re

class SimpleMapValidator:
    def __init__(self):
        self.valid_tiles = {'G', 'P', 'S', 'E', 'W'}
        self.results = []
    
    def validate_map(self, file_path):
        """Validate a single map file"""
        result = {
            'file': file_path,
            'valid': True,
            'errors': [],
            'warnings': [],
            'stats': {}
        }
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Parse sections
            metadata = self.parse_metadata(content, result)
            tiles = self.parse_tiles(content, result, metadata)
            paths = self.parse_paths(content, result)
            
            # Validate consistency
            self.validate_consistency(metadata, tiles, paths, result)
            
        except FileNotFoundError:
            result['valid'] = False
            result['errors'].append(f"File not found: {file_path}")
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"Error reading file: {str(e)}")
        
        return result
    
    def parse_metadata(self, content, result):
        """Parse [Metadata] section"""
        metadata = {}
        
        # Find metadata section
        metadata_match = re.search(r'\[Metadata\](.*?)(?=\[|$)', content, re.DOTALL)
        if not metadata_match:
            result['errors'].append("No [Metadata] section found")
            result['valid'] = False
            return metadata
        
        metadata_text = metadata_match.group(1)
        
        # Parse key-value pairs
        for line in metadata_text.split('\n'):
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                if key in ['width', 'height']:
                    try:
                        metadata[key] = int(value)
                    except ValueError:
                        result['errors'].append(f"Invalid {key}: {value}")
                        result['valid'] = False
                elif key == 'tileSize':
                    try:
                        metadata[key] = float(value)
                    except ValueError:
                        result['errors'].append(f"Invalid tileSize: {value}")
                        result['valid'] = False
                else:
                    metadata[key] = value
        
        # Check required fields
        required = ['width', 'height', 'tileSize']
        for field in required:
            if field not in metadata:
                result['errors'].append(f"Missing required metadata: {field}")
                result['valid'] = False
        
        result['stats']['metadata'] = metadata
        return metadata
    
    def parse_tiles(self, content, result, metadata):
        """Parse [Tiles] section"""
        tiles = []
        
        # Find tiles section
        tiles_match = re.search(r'\[Tiles\](.*?)(?=\[|$)', content, re.DOTALL)
        if not tiles_match:
            result['errors'].append("No [Tiles] section found")
            result['valid'] = False
            return tiles
        
        tiles_text = tiles_match.group(1)
        
        # Parse tile rows
        for line in tiles_text.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                # Remove inline comments
                if '#' in line:
                    line = line.split('#')[0].strip()
                
                if line:
                    tiles.append(line)
        
        # Validate dimensions
        if 'height' in metadata and len(tiles) != metadata['height']:
            result['errors'].append(f"Tile row count mismatch: expected {metadata['height']}, got {len(tiles)}")
            result['valid'] = False
        
        if 'width' in metadata:
            for i, row in enumerate(tiles):
                if len(row) != metadata['width']:
                    result['errors'].append(f"Row {i} width mismatch: expected {metadata['width']}, got {len(row)}")
                    result['valid'] = False
        
        # Validate tile characters
        tile_counts = {'G': 0, 'P': 0, 'S': 0, 'E': 0, 'W': 0}
        for i, row in enumerate(tiles):
            for j, char in enumerate(row):
                if char not in self.valid_tiles:
                    result['errors'].append(f"Invalid tile character '{char}' at ({j},{i})")
                    result['valid'] = False
                else:
                    tile_counts[char] += 1
        
        # Check for required tiles
        if tile_counts['S'] == 0:
            result['errors'].append("No start points (S) found")
            result['valid'] = False
        if tile_counts['E'] == 0:
            result['errors'].append("No end points (E) found")
            result['valid'] = False
        
        result['stats']['tiles'] = tile_counts
        result['stats']['total_tiles'] = sum(tile_counts.values())
        
        return tiles
    
    def parse_paths(self, content, result):
        """Parse [Paths] section"""
        paths = []
        
        # Find paths section
        paths_match = re.search(r'\[Paths\](.*?)$', content, re.DOTALL)
        if not paths_match:
            result['errors'].append("No [Paths] section found")
            result['valid'] = False
            return paths
        
        paths_text = paths_match.group(1)
        current_path = None
        
        for line in paths_text.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                # Remove inline comments
                if '#' in line:
                    line = line.split('#')[0].strip()
                
                if line.startswith('Path:'):
                    # New path declaration
                    current_path = []
                    paths.append(current_path)
                elif ',' in line and current_path is not None:
                    # Coordinate pair
                    try:
                        coords = line.split(',')
                        if len(coords) == 2:
                            x = int(coords[0].strip())
                            y = int(coords[1].strip())
                            current_path.append((x, y))
                    except ValueError:
                        result['errors'].append(f"Invalid coordinate format: {line}")
                        result['valid'] = False
        
        if not paths:
            result['errors'].append("No paths defined")
            result['valid'] = False
        
        result['stats']['path_count'] = len(paths)
        result['stats']['total_waypoints'] = sum(len(path) for path in paths)
        
        return paths
    
    def validate_consistency(self, metadata, tiles, paths, result):
        """Validate consistency between sections"""
        if not metadata or not tiles or not paths:
            return
        
        # Check path coordinates are within bounds
        width = metadata.get('width', 0)
        height = metadata.get('height', 0)
        
        for path_idx, path in enumerate(paths):
            for waypoint_idx, (x, y) in enumerate(path):
                if x < 0 or x >= width or y < 0 or y >= height:
                    result['errors'].append(f"Path {path_idx} waypoint {waypoint_idx} out of bounds: ({x},{y})")
                    result['valid'] = False
        
        # Check path tiles match waypoints
        path_tiles_in_map = set()
        for y, row in enumerate(tiles):
            for x, char in enumerate(row):
                if char in ['S', 'P', 'E']:
                    path_tiles_in_map.add((x, y))
        
        waypoints_in_paths = set()
        for path in paths:
            waypoints_in_paths.update(path)
        
        # Find mismatches
        missing_waypoints = path_tiles_in_map - waypoints_in_paths
        extra_waypoints = waypoints_in_paths - path_tiles_in_map
        
        if missing_waypoints:
            result['warnings'].append(f"Path tiles without waypoints: {missing_waypoints}")
        
        if extra_waypoints:
            result['warnings'].append(f"Waypoints without path tiles: {extra_waypoints}")
    
    def print_result(self, result):
        """Print validation result"""
        print(f"\n{'='*60}")
        print(f"Map: {os.path.basename(result['file'])}")
        print(f"Status: {'✓ VALID' if result['valid'] else '✗ INVALID'}")
        print(f"{'='*60}")
        
        # Print stats
        if 'metadata' in result['stats']:
            meta = result['stats']['metadata']
            print(f"Dimensions: {meta.get('width', '?')}x{meta.get('height', '?')}")
            print(f"Tile Size: {meta.get('tileSize', '?')}")
        
        if 'tiles' in result['stats']:
            tiles = result['stats']['tiles']
            print(f"\nTile Distribution:")
            print(f"  Start Points: {tiles['S']}")
            print(f"  End Points: {tiles['E']}")
            print(f"  Path Tiles: {tiles['P']}")
            print(f"  Buildable Ground: {tiles['G']}")
            print(f"  Obstacles: {tiles['W']}")
        
        if 'path_count' in result['stats']:
            print(f"\nPaths: {result['stats']['path_count']}")
            print(f"Total Waypoints: {result['stats']['total_waypoints']}")
        
        # Print errors
        if result['errors']:
            print(f"\nErrors:")
            for error in result['errors']:
                print(f"  ✗ {error}")
        
        # Print warnings
        if result['warnings']:
            print(f"\nWarnings:")
            for warning in result['warnings']:
                print(f"  ⚠ {warning}")

def main():
    validator = SimpleMapValidator()
    
    # Test maps to validate
    test_maps = [
        "assets/data/maps/StandardMap.map",
        "assets/data/maps/ComplexSnakeMap.map",
        "assets/data/maps/test_maps/SmallLinear.map",
        "assets/data/maps/test_maps/SmallLShape.map",
        "assets/data/maps/test_maps/MediumSpiral.map",
        "assets/data/maps/test_maps/MultiplePaths.map",
        "assets/data/maps/test_maps/LargeMaze.map",
        "assets/data/maps/test_maps/EdgeCase_SingleTile.map",
        "assets/data/maps/test_maps/EdgeCase_MaxBuildable.map",
        "assets/data/maps/test_maps/EdgeCase_MinBuildable.map",
        "assets/data/maps/test_maps/EdgeCase_AllObstacles.map",
        "assets/data/maps/test_maps/Error_MismatchedDimensions.map",
        "assets/data/maps/test_maps/Error_MissingPath.map",
        "assets/data/maps/test_maps/Error_InvalidCharacters.map"
    ]
    
    print("Tower Defense Simple Map Validator")
    print("=" * 40)
    
    valid_count = 0
    total_count = 0
    
    for map_path in test_maps:
        if os.path.exists(map_path):
            result = validator.validate_map(map_path)
            validator.print_result(result)
            
            if result['valid']:
                valid_count += 1
            total_count += 1
        else:
            print(f"\n⚠ Map file not found: {map_path}")
    
    # Summary
    print(f"\n{'='*60}")
    print(f"VALIDATION SUMMARY: {valid_count}/{total_count} maps valid")
    print(f"{'='*60}")
    
    return 0 if valid_count == total_count else 1

if __name__ == "__main__":
    sys.exit(main())
