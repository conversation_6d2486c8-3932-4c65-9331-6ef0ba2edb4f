# Tower Defense Map Testing Suite

This comprehensive testing suite validates the map loading, parsing, and rendering functionality of the Tower Defense game. It includes various test maps, debugging utilities, and automated testing tools.

## 📁 Test Map Collection

### Basic Test Maps
- **SmallLinear.map** (5x5) - Simple horizontal path for basic functionality testing
- **SmallLShape.map** (8x6) - L-shaped path for corner navigation testing  
- **MediumSpiral.map** (12x10) - Spiral path for complex routing validation
- **MultiplePaths.map** (15x10) - Two separate paths for multi-path testing
- **LargeMaze.map** (25x20) - Complex maze for stress testing and performance

### Edge Case Test Maps
- **EdgeCase_SingleTile.map** (3x3) - Minimal map with adjacent start/end points
- **EdgeCase_MaxBuildable.map** (10x8) - Maximizes buildable ground area
- **EdgeCase_MinBuildable.map** (10x8) - Minimal buildable ground for constraint testing
- **EdgeCase_AllObstacles.map** (8x6) - Maximum obstacles with minimal path

### Error Testing Maps
- **Error_MismatchedDimensions.map** - Incorrect metadata dimensions vs actual layout
- **Error_MissingPath.map** - Path tiles without path definitions
- **Error_InvalidCharacters.map** - Invalid tile characters for error handling

## 🛠️ Testing Utilities

### 1. Map Validator (`map_validator.cpp`)
Comprehensive validation tool that checks:
- ✅ Map loading and parsing
- ✅ Tile type validation and counts
- ✅ Path connectivity and consistency
- ✅ Buildability rules for melee/ranged towers
- ✅ Gameplay balance analysis
- ✅ Coordinate system validation

**Usage:**
```bash
./map_validator
```

### 2. Map Visualizer (`map_visualizer.cpp`)
ASCII art visualization tool for debugging:
- 🎨 Tile layout with coordinate grid
- 🎨 Path waypoint visualization
- 🎨 Buildability analysis grid
- 🎨 Map statistics and properties

**Usage:**
```bash
./map_visualizer [specific_map.map]  # Single map
./map_visualizer                     # All test maps
```

### 3. Automated Map Tester (`automated_map_tester.cpp`)
Batch testing with performance metrics:
- ⚡ Load time measurement
- ⚡ Validation success/failure tracking
- ⚡ Error reporting and categorization
- ⚡ Summary statistics

**Usage:**
```bash
./automated_map_tester
```

### 4. Performance Tester (`map_performance_tester.cpp`)
Benchmarking tool for performance analysis:
- 🚀 Map loading performance
- 🚀 Tile access speed
- 🚀 Coordinate conversion efficiency
- 🚀 Buildability query performance
- 🚀 Path access benchmarks

**Usage:**
```bash
./map_performance_tester
```

## 🚀 Quick Start

### Building the Test Suite

1. **Add to CMakeLists.txt:**
```cmake
# Add the content from test_utilities_CMakeLists.txt to your main CMakeLists.txt
```

2. **Build the project:**
```bash
mkdir -p build && cd build
cmake ..
make -j$(nproc)
```

3. **Run all tests:**
```bash
./run_map_tests.sh
```

### Manual Testing

**Individual test utilities:**
```bash
# Validate all maps
./map_validator

# Visualize specific map
./map_visualizer ../assets/data/maps/test_maps/SmallLShape.map

# Run automated tests
./automated_map_tester

# Performance benchmarks
./map_performance_tester
```

## 📊 Map Format Specification

### File Structure
```
[Metadata]
name = Map Name
width = 12
height = 10
tileSize = 64.0
initialLives = 20
initialGold = 500

[Tiles]
WWWWWWWWWWWW
WGGGGGGGGGWW
WSPPPPPPPEWW
...

[Paths]
Path:0
2,2   # S - start point
3,2   # P - path
...
```

### Tile Character Mapping
| Character | TileType | Description |
|-----------|----------|-------------|
| `G` | BUILDABLE_GROUND | Remote towers can build here |
| `P` | PATH | Enemy movement, melee towers can build |
| `S` | START_POINT | Enemy spawn point |
| `E` | END_POINT | Enemy destination |
| `W` | OBSTACLE | Impassable walls |

### Validation Rules
1. **Dimensions**: Tile layout must match metadata width/height
2. **Paths**: All path tiles must have corresponding waypoints
3. **Connectivity**: Paths must connect start to end points
4. **Buildability**: Buildable ground must be adjacent to paths
5. **Completeness**: All waypoints must reference valid path tiles

## 🎯 Test Coverage

### Functional Tests
- ✅ Map loading from file
- ✅ Metadata parsing (dimensions, tile size, game settings)
- ✅ Tile layout parsing and validation
- ✅ Path definition parsing and waypoint extraction
- ✅ Coordinate system conversion (grid ↔ world)
- ✅ Tile type queries and validation
- ✅ Buildability rules for different tower types
- ✅ Path connectivity and consistency

### Edge Cases
- ✅ Minimal maps (3x3)
- ✅ Large maps (25x20+)
- ✅ Single-tile paths
- ✅ Multiple independent paths
- ✅ Complex maze-like layouts
- ✅ Maximum/minimum buildable areas

### Error Handling
- ✅ Malformed file formats
- ✅ Dimension mismatches
- ✅ Invalid tile characters
- ✅ Missing path definitions
- ✅ Disconnected paths
- ✅ Invalid coordinates

### Performance Tests
- ✅ Map loading speed
- ✅ Tile access performance
- ✅ Coordinate conversion efficiency
- ✅ Buildability query speed
- ✅ Memory usage patterns

## 📈 Expected Results

### Valid Maps Should:
- Load successfully without errors
- Pass all validation checks
- Have consistent tile/path mapping
- Support both melee and ranged tower placement
- Maintain reasonable performance metrics

### Invalid Maps Should:
- Fail gracefully with descriptive error messages
- Not crash the application
- Provide clear indication of the problem
- Allow for debugging and correction

## 🔧 Extending the Test Suite

### Adding New Test Maps
1. Create `.map` file in `assets/data/maps/test_maps/`
2. Follow the StandardMap.map format
3. Add to test utility map lists
4. Run validation to ensure correctness

### Adding New Test Cases
1. Extend validation logic in `map_validator.cpp`
2. Add performance tests in `map_performance_tester.cpp`
3. Update visualization in `map_visualizer.cpp`
4. Include in automated test suite

### Custom Validation Rules
Modify the `MapValidator` class to add game-specific validation:
- Tower placement constraints
- Enemy pathfinding requirements
- Gameplay balance metrics
- Visual rendering requirements

## 🐛 Troubleshooting

### Common Issues
1. **Build Errors**: Ensure Qt6 and all dependencies are installed
2. **File Not Found**: Check that map files exist in correct directories
3. **Permission Errors**: Ensure `run_map_tests.sh` is executable
4. **Performance Issues**: Large maps may require more memory/time

### Debug Tips
1. Use `map_visualizer` to inspect map layout visually
2. Check `map_validator` output for detailed error messages
3. Run individual test utilities for focused debugging
4. Enable Qt debug output for detailed parsing information

This testing suite ensures robust map functionality and provides tools for ongoing development and debugging of the Tower Defense game's map system.
