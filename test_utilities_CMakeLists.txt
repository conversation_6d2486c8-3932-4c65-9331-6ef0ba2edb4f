# CMakeLists.txt for Map Testing Utilities
# Add this content to your main CMakeLists.txt or create separate test targets

# Map Validator
add_executable(map_validator map_validator.cpp)
target_link_libraries(map_validator 
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(map_validator PRIVATE include)

# Map Visualizer
add_executable(map_visualizer map_visualizer.cpp)
target_link_libraries(map_visualizer 
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(map_visualizer PRIVATE include)

# Automated Map Tester
add_executable(automated_map_tester automated_map_tester.cpp)
target_link_libraries(automated_map_tester 
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(automated_map_tester PRIVATE include)

# Map Performance Tester
add_executable(map_performance_tester map_performance_tester.cpp)
target_link_libraries(map_performance_tester 
    GameLogic
    Qt6::Core
    Qt6::Gui
)
target_include_directories(map_performance_tester PRIVATE include)

# Existing test programs (if they exist)
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test_map_loading.cpp")
    add_executable(test_map_loading test_map_loading.cpp)
    target_link_libraries(test_map_loading 
        GameLogic
        Qt6::Core
        Qt6::Gui
    )
    target_include_directories(test_map_loading PRIVATE include)
endif()

if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/comprehensive_map_test.cpp")
    add_executable(comprehensive_map_test comprehensive_map_test.cpp)
    target_link_libraries(comprehensive_map_test 
        GameLogic
        Qt6::Core
        Qt6::Gui
    )
    target_include_directories(comprehensive_map_test PRIVATE include)
endif()

# Copy test maps to build directory for easier testing
file(COPY assets/data/maps DESTINATION ${CMAKE_BINARY_DIR}/assets/data/)

# Create a custom target to run all map tests
add_custom_target(run_map_tests
    COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/run_map_tests.sh
    DEPENDS map_validator map_visualizer automated_map_tester map_performance_tester
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running comprehensive map tests"
)
