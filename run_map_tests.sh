#!/bin/bash

# Tower Defense Map Testing Suite
# Comprehensive testing script for all map-related functionality

echo "=========================================="
echo "Tower Defense Map Testing Suite"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if build directory exists
if [ ! -d "build" ]; then
    echo -e "${RED}Error: Build directory not found. Please run cmake and build the project first.${NC}"
    exit 1
fi

cd build

echo -e "${BLUE}Building test utilities...${NC}"
make -j$(nproc) 2>/dev/null || make -j4 2>/dev/null || make

echo ""
echo -e "${BLUE}1. Running Automated Map Tests...${NC}"
echo "----------------------------------------"
if [ -f "./automated_map_tester" ]; then
    ./automated_map_tester
    AUTOMATED_RESULT=$?
else
    echo -e "${RED}automated_map_tester not found${NC}"
    AUTOMATED_RESULT=1
fi

echo ""
echo -e "${BLUE}2. Running Map Validation...${NC}"
echo "----------------------------------------"
if [ -f "./map_validator" ]; then
    ./map_validator
    VALIDATOR_RESULT=$?
else
    echo -e "${RED}map_validator not found${NC}"
    VALIDATOR_RESULT=1
fi

echo ""
echo -e "${BLUE}3. Running Performance Tests...${NC}"
echo "----------------------------------------"
if [ -f "./map_performance_tester" ]; then
    ./map_performance_tester
    PERFORMANCE_RESULT=$?
else
    echo -e "${RED}map_performance_tester not found${NC}"
    PERFORMANCE_RESULT=1
fi

echo ""
echo -e "${BLUE}4. Running Map Visualization (Sample)...${NC}"
echo "----------------------------------------"
if [ -f "./map_visualizer" ]; then
    echo "Visualizing SmallLShape.map as example:"
    ./map_visualizer ../assets/data/maps/test_maps/SmallLShape.map
    VISUALIZER_RESULT=$?
else
    echo -e "${RED}map_visualizer not found${NC}"
    VISUALIZER_RESULT=1
fi

echo ""
echo -e "${BLUE}5. Running Existing Test Programs...${NC}"
echo "----------------------------------------"
if [ -f "./test_map_loading" ]; then
    echo "Running test_map_loading:"
    ./test_map_loading
    LOADING_RESULT=$?
else
    echo -e "${YELLOW}test_map_loading not found (optional)${NC}"
    LOADING_RESULT=0
fi

if [ -f "./comprehensive_map_test" ]; then
    echo "Running comprehensive_map_test:"
    ./comprehensive_map_test
    COMPREHENSIVE_RESULT=$?
else
    echo -e "${YELLOW}comprehensive_map_test not found (optional)${NC}"
    COMPREHENSIVE_RESULT=0
fi

# Summary
echo ""
echo "=========================================="
echo "TEST SUMMARY"
echo "=========================================="

if [ $AUTOMATED_RESULT -eq 0 ]; then
    echo -e "Automated Tests:     ${GREEN}PASSED${NC}"
else
    echo -e "Automated Tests:     ${RED}FAILED${NC}"
fi

if [ $VALIDATOR_RESULT -eq 0 ]; then
    echo -e "Map Validation:      ${GREEN}PASSED${NC}"
else
    echo -e "Map Validation:      ${RED}FAILED${NC}"
fi

if [ $PERFORMANCE_RESULT -eq 0 ]; then
    echo -e "Performance Tests:   ${GREEN}PASSED${NC}"
else
    echo -e "Performance Tests:   ${RED}FAILED${NC}"
fi

if [ $VISUALIZER_RESULT -eq 0 ]; then
    echo -e "Map Visualization:   ${GREEN}PASSED${NC}"
else
    echo -e "Map Visualization:   ${RED}FAILED${NC}"
fi

if [ $LOADING_RESULT -eq 0 ]; then
    echo -e "Loading Tests:       ${GREEN}PASSED${NC}"
else
    echo -e "Loading Tests:       ${RED}FAILED${NC}"
fi

if [ $COMPREHENSIVE_RESULT -eq 0 ]; then
    echo -e "Comprehensive Tests: ${GREEN}PASSED${NC}"
else
    echo -e "Comprehensive Tests: ${RED}FAILED${NC}"
fi

# Overall result
TOTAL_FAILURES=$((AUTOMATED_RESULT + VALIDATOR_RESULT + PERFORMANCE_RESULT + VISUALIZER_RESULT + LOADING_RESULT + COMPREHENSIVE_RESULT))

echo ""
if [ $TOTAL_FAILURES -eq 0 ]; then
    echo -e "${GREEN}=========================================="
    echo "ALL TESTS PASSED!"
    echo -e "==========================================${NC}"
    exit 0
else
    echo -e "${RED}=========================================="
    echo "SOME TESTS FAILED ($TOTAL_FAILURES failures)"
    echo -e "==========================================${NC}"
    exit 1
fi
